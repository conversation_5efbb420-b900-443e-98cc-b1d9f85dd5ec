"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { useTheme } from "@/stores/themeStore"
import { Monitor, Moon, Settings, Sun } from "lucide-react"
import React, { useState } from "react"

interface SettingsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsModal({ open, onOpenChange }: SettingsModalProps) {
  const { theme, setTheme } = useTheme()
  const [activeTab, setActiveTab] = useState("general")

  const themeOptions = [
    {
      value: 'light' as const,
      label: 'Light',
      icon: Sun,
      description: 'Light theme'
    },
    {
      value: 'dark' as const,
      label: 'Dark',
      icon: Moon,
      description: 'Dark theme'
    },
    {
      value: 'system' as const,
      label: 'System',
      icon: Monitor,
      description: 'Follow system preference'
    }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md h-[500px] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Settings
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full flex flex-col flex-1 min-h-0">
          <TabsList className="grid w-full grid-cols-2 flex-shrink-0">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="editor">Editor</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="flex-1 overflow-y-auto mt-6">
            <div className="space-y-6 pr-2">
              <div className="space-y-3">
                <label className="text-sm font-medium text-foreground">
                  Theme
                </label>
                <div className="grid gap-2">
                  {themeOptions.map((option) => {
                    const Icon = option.icon
                    const isSelected = theme === option.value

                    return (
                      <Button
                        key={option.value}
                        variant={isSelected ? "default" : "outline"}
                        className={cn(
                          "w-full justify-start gap-3 h-auto py-3",
                          isSelected && "bg-primary-500 text-white hover:bg-primary-600"
                        )}
                        onClick={() => setTheme(option.value)}
                      >
                        <Icon className="w-4 h-4" />
                        <div className="text-left">
                          <div className="font-medium">{option.label}</div>
                          <div className={cn(
                            "text-xs",
                            isSelected ? "text-white/80" : "text-muted-foreground"
                          )}>
                            {option.description}
                          </div>
                        </div>
                      </Button>
                    )
                  })}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="editor" className="flex-1 overflow-y-auto mt-6">
            <div className="space-y-6 pr-2">
              <div className="space-y-3">
                <label className="text-sm font-medium text-foreground">
                  Editor Settings
                </label>
                <div className="text-sm text-muted-foreground">
                  Editor-specific settings will be available here.
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
